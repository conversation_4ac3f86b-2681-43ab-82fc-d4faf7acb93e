import React, { useState } from 'react';
import { Card, Text, Heading } from '../../../../components/global';
import { Button, Input } from '../../../../components/forms';

export interface ProductsViewProps {
  className?: string;
  'data-testid'?: string;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  status: 'active' | 'inactive' | 'discontinued';
  description: string;
}

const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Enterprise Software License',
    sku: 'ESL-001',
    category: 'Software',
    price: 2500,
    stock: 100,
    status: 'active',
    description: 'Full enterprise software license with support',
  },
  {
    id: '2',
    name: 'Professional Services Package',
    sku: 'PSP-002',
    category: 'Services',
    price: 5000,
    stock: 50,
    status: 'active',
    description: 'Comprehensive professional services package',
  },
  {
    id: '3',
    name: 'Training Workshop',
    sku: 'TW-003',
    category: 'Training',
    price: 1200,
    stock: 25,
    status: 'active',
    description: 'Two-day intensive training workshop',
  },
  {
    id: '4',
    name: 'Legacy System Integration',
    sku: 'LSI-004',
    category: 'Integration',
    price: 8000,
    stock: 10,
    status: 'discontinued',
    description: 'Legacy system integration service',
  },
];

const getStatusColor = (status: Product['status']) => {
  switch (status) {
    case 'active':
      return 'text-green-600 bg-green-100 dark:bg-green-900';
    case 'inactive':
      return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900';
    case 'discontinued':
      return 'text-red-600 bg-red-100 dark:bg-red-900';
    default:
      return 'text-gray-600 bg-gray-100 dark:bg-gray-900';
  }
};

const getStockStatus = (stock: number) => {
  if (stock === 0) return { text: 'Out of Stock', color: 'text-red-600' };
  if (stock < 10) return { text: 'Low Stock', color: 'text-yellow-600' };
  return { text: 'In Stock', color: 'text-green-600' };
};

/**
 * Products View Component
 * 
 * Displays and manages product catalog with inventory tracking.
 */
export const ProductsView: React.FC<ProductsViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<Product['status'] | 'all'>('all');

  const categories = Array.from(new Set(mockProducts.map(p => p.category)));

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || product.status === statusFilter;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  return (
    <div className={`space-y-6 ${className}`} data-testid={testId}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <Heading level={2}>Products</Heading>
          <Text variant="body2" color="secondary">
            Manage product catalog and inventory
          </Text>
        </div>
        <Button variant="primary">
          Add New Product
        </Button>
      </div>

      {/* Filters */}
      <Card variant="outlined" padding="lg">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="sm:w-48">
            <select
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
          <div className="sm:w-48">
            <select
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as Product['status'] | 'all')}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="discontinued">Discontinued</option>
            </select>
          </div>
        </div>
      </Card>

      {/* Product Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card variant="elevated" padding="lg">
          <Text variant="caption" color="secondary" className="uppercase tracking-wide">
            Total Products
          </Text>
          <Text variant="h3" className="mt-1">
            {mockProducts.length}
          </Text>
        </Card>
        <Card variant="elevated" padding="lg">
          <Text variant="caption" color="secondary" className="uppercase tracking-wide">
            Active Products
          </Text>
          <Text variant="h3" className="mt-1 text-green-600">
            {mockProducts.filter(p => p.status === 'active').length}
          </Text>
        </Card>
        <Card variant="elevated" padding="lg">
          <Text variant="caption" color="secondary" className="uppercase tracking-wide">
            Low Stock Items
          </Text>
          <Text variant="h3" className="mt-1 text-yellow-600">
            {mockProducts.filter(p => p.stock < 10 && p.stock > 0).length}
          </Text>
        </Card>
        <Card variant="elevated" padding="lg">
          <Text variant="caption" color="secondary" className="uppercase tracking-wide">
            Total Value
          </Text>
          <Text variant="h3" className="mt-1 text-purple-600">
            ${mockProducts.reduce((sum, p) => sum + (p.price * p.stock), 0).toLocaleString()}
          </Text>
        </Card>
      </div>

      {/* Products List */}
      <Card variant="elevated" padding="none">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  SKU
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {filteredProducts.map((product) => {
                const stockStatus = getStockStatus(product.stock);
                return (
                  <tr key={product.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <Text variant="body2" className="font-medium">
                          {product.name}
                        </Text>
                        <Text variant="caption" color="secondary">
                          {product.description}
                        </Text>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Text variant="body2" className="font-mono">
                        {product.sku}
                      </Text>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Text variant="body2">
                        {product.category}
                      </Text>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Text variant="body2" className="font-medium">
                        ${product.price.toLocaleString()}
                      </Text>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <Text variant="body2" className="font-medium">
                          {product.stock}
                        </Text>
                        <Text variant="caption" className={stockStatus.color}>
                          {stockStatus.text}
                        </Text>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(product.status)}`}>
                        {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        
        {filteredProducts.length === 0 && (
          <div className="text-center py-12">
            <Text variant="body1" color="secondary">
              No products found matching your criteria.
            </Text>
          </div>
        )}
      </Card>
    </div>
  );
};

export default ProductsView;
