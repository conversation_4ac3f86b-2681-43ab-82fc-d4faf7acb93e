// Module Types
// Type definitions for feature modules

import type { ReactNode } from 'react';
import type { User, UserRole, UserStatus } from './user';
import type { App } from './app';

// Re-export module-specific types for centralized access
// This allows importing all module types from a single location

// User Management Module Types
export interface UserManagementUser extends User {
  // Additional fields specific to user management
  invitedBy?: string;
  invitedAt?: Date;
  activatedAt?: Date;
  deactivatedAt?: Date;
  deactivatedBy?: string;
  deactivationReason?: string;
  groups?: UserGroup[];
  departments?: Department[];
  teams?: Team[];
}

export interface UserGroup {
  id: string;
  name: string;
  description?: string;
  permissions: string[];
  memberCount: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  managerId?: string;
  memberCount: number;
  budget?: number;
  location?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Team {
  id: string;
  name: string;
  description?: string;
  leaderId: string;
  departmentId?: string;
  members: string[];
  projects?: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Discuss Module Types
export interface DiscussUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status: UserStatus;
  lastSeen?: Date;
  isOnline: boolean;
  timezone?: string;
  preferences: DiscussUserPreferences;
}

export interface DiscussUserPreferences {
  notifications: {
    mentions: boolean;
    directMessages: boolean;
    channelMessages: boolean;
    emailNotifications: boolean;
    pushNotifications: boolean;
    soundEnabled: boolean;
  };
  appearance: {
    theme: 'light' | 'dark' | 'system';
    compactMode: boolean;
    showAvatars: boolean;
    showTimestamps: boolean;
    messageGrouping: boolean;
  };
  privacy: {
    showOnlineStatus: boolean;
    allowDirectMessages: boolean;
    readReceipts: boolean;
  };
}

export interface Message {
  id: string;
  content: string;
  authorId: string;
  channelId?: string;
  threadId?: string;
  parentMessageId?: string;
  timestamp: Date;
  editedAt?: Date;
  reactions: Reaction[];
  attachments: Attachment[];
  mentions: string[];
  isDeleted: boolean;
  deliveryStatus: 'sent' | 'delivered' | 'read' | 'failed';
  type: 'text' | 'file' | 'image' | 'system' | 'call';
  metadata?: Record<string, any>;
}

export interface Channel {
  id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct';
  memberIds: string[];
  createdBy: string;
  createdAt: Date;
  lastActivity?: Date;
  isArchived: boolean;
  settings: ChannelSettings;
  unreadCount?: number;
  lastMessage?: Message;
}

export interface ChannelSettings {
  allowFileUploads: boolean;
  allowReactions: boolean;
  allowThreads: boolean;
  allowEditing: boolean;
  allowDeletion: boolean;
  retentionPeriod?: number;
  moderationEnabled: boolean;
  slowMode?: number;
  maxMessageLength?: number;
}

export interface Reaction {
  emoji: string;
  userIds: string[];
  count: number;
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  metadata?: Record<string, any>;
}

export interface Thread {
  id: string;
  parentMessageId: string;
  channelId: string;
  messageCount: number;
  participantIds: string[];
  lastActivity: Date;
  isArchived: boolean;
}

// Sales Module Types
export interface SalesLead {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  title?: string;
  source: LeadSource;
  status: LeadStatus;
  score: number;
  assignedTo?: string;
  createdAt: Date;
  updatedAt: Date;
  lastContactedAt?: Date;
  nextFollowUpAt?: Date;
  notes: Note[];
  activities: Activity[];
  customFields?: Record<string, any>;
}

export type LeadSource = 
  | 'website' 
  | 'referral' 
  | 'social_media' 
  | 'email_campaign' 
  | 'cold_call' 
  | 'trade_show' 
  | 'advertisement' 
  | 'other';

export type LeadStatus = 
  | 'new' 
  | 'contacted' 
  | 'qualified' 
  | 'proposal' 
  | 'negotiation' 
  | 'closed_won' 
  | 'closed_lost';

export interface Opportunity {
  id: string;
  name: string;
  description?: string;
  leadId?: string;
  accountId: string;
  contactId: string;
  ownerId: string;
  amount: number;
  currency: string;
  stage: OpportunityStage;
  probability: number;
  expectedCloseDate: Date;
  actualCloseDate?: Date;
  source: LeadSource;
  competitors?: string[];
  products: OpportunityProduct[];
  createdAt: Date;
  updatedAt: Date;
  notes: Note[];
  activities: Activity[];
}

export type OpportunityStage = 
  | 'prospecting' 
  | 'qualification' 
  | 'needs_analysis' 
  | 'proposal' 
  | 'negotiation' 
  | 'closed_won' 
  | 'closed_lost';

export interface OpportunityProduct {
  productId: string;
  quantity: number;
  unitPrice: number;
  discount?: number;
  totalPrice: number;
}

export interface Account {
  id: string;
  name: string;
  type: AccountType;
  industry?: string;
  website?: string;
  phone?: string;
  email?: string;
  address: Address;
  ownerId: string;
  parentAccountId?: string;
  employees?: number;
  annualRevenue?: number;
  createdAt: Date;
  updatedAt: Date;
  contacts: Contact[];
  opportunities: Opportunity[];
  notes: Note[];
  activities: Activity[];
}

export type AccountType = 'prospect' | 'customer' | 'partner' | 'competitor' | 'other';

export interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  department?: string;
  accountId: string;
  ownerId: string;
  isPrimary: boolean;
  leadSource?: LeadSource;
  createdAt: Date;
  updatedAt: Date;
  notes: Note[];
  activities: Activity[];
}

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
}

export interface Note {
  id: string;
  content: string;
  authorId: string;
  createdAt: Date;
  updatedAt: Date;
  isPrivate: boolean;
  tags?: string[];
}

export interface Activity {
  id: string;
  type: ActivityType;
  subject: string;
  description?: string;
  status: ActivityStatus;
  priority: 'low' | 'medium' | 'high';
  assignedTo: string;
  relatedTo: {
    type: 'lead' | 'opportunity' | 'account' | 'contact';
    id: string;
  };
  scheduledAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  reminder?: {
    enabled: boolean;
    time: Date;
    sent: boolean;
  };
}

export type ActivityType = 
  | 'call' 
  | 'email' 
  | 'meeting' 
  | 'task' 
  | 'demo' 
  | 'proposal' 
  | 'follow_up' 
  | 'other';

export type ActivityStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';

// Module Configuration
export interface ModuleConfig {
  id: string;
  name: string;
  version: string;
  description?: string;
  enabled: boolean;
  permissions: string[];
  dependencies: string[];
  routes: ModuleRoute[];
  components: ModuleComponent[];
  services: ModuleService[];
  settings: ModuleSettings;
  metadata: ModuleMetadata;
}

export interface ModuleRoute {
  path: string;
  component: string;
  exact?: boolean;
  permissions?: string[];
  title?: string;
  description?: string;
  icon?: ReactNode;
}

export interface ModuleComponent {
  name: string;
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  permissions?: string[];
}

export interface ModuleService {
  name: string;
  service: any;
  config?: Record<string, any>;
}

export interface ModuleSettings {
  [key: string]: any;
}

export interface ModuleMetadata {
  author?: string;
  license?: string;
  repository?: string;
  homepage?: string;
  keywords?: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

// Module Registry
export interface ModuleRegistry {
  modules: Map<string, ModuleConfig>;
  register: (config: ModuleConfig) => void;
  unregister: (moduleId: string) => void;
  get: (moduleId: string) => ModuleConfig | undefined;
  getAll: () => ModuleConfig[];
  getEnabled: () => ModuleConfig[];
  enable: (moduleId: string) => void;
  disable: (moduleId: string) => void;
  hasPermission: (moduleId: string, permission: string, userPermissions: string[]) => boolean;
}

// Module Context
export interface ModuleContext {
  currentModule?: string;
  availableModules: ModuleConfig[];
  enabledModules: ModuleConfig[];
  userPermissions: string[];
  settings: Record<string, any>;
  isLoading: boolean;
  error?: string;
}
