// Application Configuration
// Core application settings and metadata

import type { AppConfiguration } from '../types/configuration';

// Application Information
export const APP_INFO = {
  name: 'Nexed Web',
  version: '1.0.0',
  description: 'Modern web application platform',
  environment: (import.meta.env.MODE || 'development') as 'development' | 'staging' | 'production' | 'test',
  buildNumber: import.meta.env.VITE_BUILD_NUMBER || 'dev',
  buildDate: import.meta.env.VITE_BUILD_DATE || new Date().toISOString(),
  gitCommit: import.meta.env.VITE_GIT_COMMIT || 'unknown',
  gitBranch: import.meta.env.VITE_GIT_BRANCH || 'unknown',
} as const;

// Security Configuration
export const SECURITY_CONFIG = {
  // Authentication
  sessionTimeout: 3600000, // 1 hour in milliseconds
  maxLoginAttempts: 5,
  lockoutDuration: 900000, // 15 minutes
  passwordMinLength: 8,
  passwordRequireSpecialChars: true,
  enableTwoFactor: false,
  
  // Authorization
  enableRBAC: true,
  defaultPermissions: ['read:profile', 'update:profile'],
  adminPermissions: ['*'],
  
  // Data Protection
  enableEncryption: true,
  encryptionAlgorithm: 'AES-256-GCM',
  enableDataMasking: true,
  enableAuditTrail: true,
  
  // Network Security
  enableCSP: true,
  enableCORS: true,
  allowedOrigins: [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://nexed.app',
    'https://*.nexed.app',
  ],
  enableHTTPS: APP_INFO.environment === 'production',
  enableHSTS: APP_INFO.environment === 'production',
} as const;

// Performance Configuration
export const PERFORMANCE_CONFIG = {
  // Caching
  enableCaching: true,
  cacheTimeout: 300000, // 5 minutes
  maxCacheSize: 100 * 1024 * 1024, // 100MB
  
  // Loading
  enableLazyLoading: true,
  enableCodeSplitting: true,
  preloadCriticalResources: true,
  
  // Optimization
  enableImageOptimization: true,
  enableAssetCompression: true,
  enableServiceWorker: APP_INFO.environment === 'production',
  
  // Memory Management
  maxMemoryUsage: 512 * 1024 * 1024, // 512MB
  enableMemoryMonitoring: APP_INFO.environment !== 'production',
  garbageCollectionInterval: 60000, // 1 minute
} as const;

// Logging Configuration
export const LOGGING_CONFIG = {
  level: (APP_INFO.environment === 'production' ? 'warn' : 'debug') as 'error' | 'warn' | 'info' | 'debug' | 'trace',
  enableConsoleLogging: APP_INFO.environment !== 'production',
  enableFileLogging: false,
  enableRemoteLogging: APP_INFO.environment === 'production',
  maxLogSize: 10 * 1024 * 1024, // 10MB
  logRetentionDays: APP_INFO.environment === 'production' ? 90 : 7,
  logFormat: 'json' as 'json' | 'text',
  enableStructuredLogging: true,
  sensitiveFields: ['password', 'token', 'apiKey', 'secret', 'creditCard'],
} as const;

// Backup Configuration
export const BACKUP_CONFIG = {
  enableAutoBackup: false,
  backupInterval: 24 * 60 * 60 * 1000, // 24 hours
  retentionPeriod: 30 * 24 * 60 * 60 * 1000, // 30 days
  enableCloudBackup: false,
  backupLocation: '/backups',
  compressionEnabled: true,
  encryptionEnabled: true,
} as const;

// Monitoring Configuration
export const MONITORING_CONFIG = {
  enableHealthChecks: true,
  healthCheckInterval: 30000, // 30 seconds
  enablePerformanceMonitoring: true,
  enableErrorTracking: true,
  enableUptimeMonitoring: APP_INFO.environment === 'production',
  enableResourceMonitoring: true,
  alertThresholds: {
    cpuUsage: 80,
    memoryUsage: 85,
    diskUsage: 90,
    responseTime: 2000,
    errorRate: 5,
    uptimePercentage: 99.9,
  },
} as const;

// Development Configuration (only in dev/staging)
export const DEVELOPMENT_CONFIG = APP_INFO.environment !== 'production' ? {
  enableHotReload: true,
  enableSourceMaps: true,
  enableProfiling: false,
  enableStorybook: true,
  enableTestMode: APP_INFO.environment === 'test',
  mockApiResponses: APP_INFO.environment === 'development',
  enableDevLogin: true,
  enableDebugPanel: true,
  enablePerformancePanel: true,
  enableReduxDevTools: true,
} : undefined;

// Main Application Configuration
export const APP_CONFIG: AppConfiguration = {
  app: APP_INFO,
  api: {
    baseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
    enableCaching: PERFORMANCE_CONFIG.enableCaching,
    cacheTimeout: PERFORMANCE_CONFIG.cacheTimeout,
    enableMocking: DEVELOPMENT_CONFIG?.mockApiResponses || false,
    mockDelay: 200,
    endpoints: {
      auth: '/auth',
      users: '/users',
      apps: '/apps',
      files: '/files',
      notifications: '/notifications',
      settings: '/settings',
    },
    headers: {
      'Content-Type': 'application/json',
      'X-App-Version': APP_INFO.version,
      'X-Build-Number': APP_INFO.buildNumber,
    },
  },
  features: {
    // Core Features
    enableAnalytics: APP_INFO.environment === 'production',
    enableNotifications: true,
    enableDarkMode: true,
    enableRealTimeUpdates: true,
    
    // Advanced Features
    enableAdvancedSearch: true,
    enableBetaFeatures: APP_INFO.environment !== 'production',
    enableExperimentalUI: APP_INFO.environment === 'development',
    enableDebugMode: APP_INFO.environment === 'development',
    
    // Enterprise Features
    enableSSOLogin: false,
    enableAuditLogging: SECURITY_CONFIG.enableAuditTrail,
    enableDataExport: true,
    enableAdvancedReporting: true,
    
    // Module Features
    enableDiscussModule: true,
    enableSalesModule: true,
    enableUserManagement: true,
    enableTaskManagement: true,
    enableFileManagement: true,
    enableCalendarModule: true,
    enableReportsModule: true,
    enableSettingsModule: true,
  },
  ui: {
    // Theme Configuration
    defaultTheme: 'system' as 'light' | 'dark' | 'system',
    allowThemeToggle: true,
    customThemes: [],
    
    // Layout Settings
    sidebarCollapsed: false,
    compactMode: false,
    showTooltips: true,
    animationsEnabled: true,
    reducedMotion: false,
    highContrastMode: false,
    
    // Data Display
    itemsPerPage: 25,
    maxItemsPerPage: 100,
    enableVirtualScrolling: true,
    enableInfiniteScroll: false,
    
    // Accessibility
    enableScreenReader: true,
    enableKeyboardNavigation: true,
    focusIndicatorVisible: true,
    
    // Localization
    defaultLanguage: 'en' as 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'zh' | 'ja',
    supportedLanguages: ['en', 'es', 'fr', 'de', 'it', 'pt', 'zh', 'ja'],
    dateFormat: 'MM/dd/yyyy' as 'MM/dd/yyyy' | 'dd/MM/yyyy' | 'yyyy-MM-dd' | 'dd-MM-yyyy',
    timeFormat: '12h' as '12h' | '24h',
    timezone: 'UTC',
    enableRTL: false,
  },
  performance: PERFORMANCE_CONFIG,
  security: SECURITY_CONFIG,
  logging: LOGGING_CONFIG,
  integrations: {
    // External Services
    enableGoogleMaps: false,
    enableStripePayments: false,
    enableSlackNotifications: false,
    enableZendeskSupport: false,
    
    // Social Login
    enableGoogleLogin: false,
    enableMicrosoftLogin: false,
    enableGitHubLogin: false,
    enableLinkedInLogin: false,
    
    // Analytics
    enableGoogleAnalytics: APP_INFO.environment === 'production',
    enableMixpanel: false,
    enableHotjar: false,
    
    // Error Tracking
    enableSentry: APP_INFO.environment === 'production',
    enableBugsnag: false,
    enableRollbar: false,
    
    // Communication
    enableTwilio: false,
    enableSendGrid: false,
    enableMailchimp: false,
  },
  backup: BACKUP_CONFIG,
  monitoring: MONITORING_CONFIG,
  development: DEVELOPMENT_CONFIG,
};

// Configuration getter with environment overrides
export function getAppConfig(): AppConfiguration {
  return APP_CONFIG;
}

// Configuration validator
export function validateConfig(config: AppConfiguration): boolean {
  try {
    // Basic validation
    if (!config.app?.name || !config.app?.version) {
      console.error('Invalid app configuration: missing name or version');
      return false;
    }
    
    if (!config.api?.baseUrl) {
      console.error('Invalid API configuration: missing baseUrl');
      return false;
    }
    
    // Environment-specific validation
    if (config.app.environment === 'production') {
      if (config.development) {
        console.warn('Development configuration should not be present in production');
      }
      
      if (!config.security.enableHTTPS) {
        console.error('HTTPS must be enabled in production');
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Configuration validation failed:', error);
    return false;
  }
}

// Export individual configurations for specific use cases
export {
  APP_INFO,
  SECURITY_CONFIG,
  PERFORMANCE_CONFIG,
  LOGGING_CONFIG,
  BACKUP_CONFIG,
  MONITORING_CONFIG,
  DEVELOPMENT_CONFIG,
};
